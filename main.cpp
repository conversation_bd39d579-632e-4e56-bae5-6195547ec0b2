#include <Windows.h>

#include "sdk/sdk.hpp"
#include "ven.zed/ven_zed_module.h"
#include "champion_module.h"

// Global champion instance
static ChampionModule* g_champion_module = nullptr;

extern "C" __declspec(dllexport) int SDKVersion = SDK_VERSION;

extern "C" __declspec(dllexport) bool PluginLoad(core_sdk* sdk, void** custom_sdk) {
    g_sdk = sdk;

    // Initialize required VEN SDKs
    if (!sdk_init::common()) {
        g_sdk->log_console("[-] Failed to initialize common SDK");
        return false;
    }

    if (!sdk_init::orbwalker()) {
        g_sdk->log_console("[-] Failed to initialize orbwalker SDK");
        return false;
    }

    if (!sdk_init::prediction()) {
        g_sdk->log_console("[-] Failed to initialize prediction SDK");
        return false;
    }

    if (!sdk_init::target_selector()) {
        g_sdk->log_console("[-] Failed to initialize target selector SDK");
        return false;
    }

    // Get local player
    auto local_player = g_sdk->object_manager->get_local_player();
    if (!local_player) {
        g_sdk->log_console("[-] Failed to get local player");
        return false;
    }

    // Load appropriate champion module
    std::string champion_name = local_player->get_char_name();

    if (champion_name == "Zed") {
        g_champion_module = new VenZedModule();
        g_sdk->log_console("[+] Loading ven.zed module");
    }
    // Future champions:
    // else if (champion_name == "Yasuo") {
    //     g_champion_module = new VenYasuoModule();
    //     g_sdk->log_console("[+] Loading ven.yasuo module");
    // }
    else {
        g_sdk->log_console("[-] Champion '%s' not supported", champion_name.c_str());
        return false;
    }

    // Initialize the champion module
    if (g_champion_module && !g_champion_module->Initialize()) {
        g_sdk->log_console("[-] Failed to initialize %s module", champion_name.c_str());
        delete g_champion_module;
        g_champion_module = nullptr;
        return false;
    }

    g_sdk->log_console("[+] VEN Solutions loaded for %s!", champion_name.c_str());
    return true;
}

extern "C" __declspec(dllexport) void PluginUnload() {
    if (g_champion_module) {
        g_champion_module->Shutdown();
        delete g_champion_module;
        g_champion_module = nullptr;
    }

    g_sdk->log_console("[-] VEN Solutions unloaded!");
}