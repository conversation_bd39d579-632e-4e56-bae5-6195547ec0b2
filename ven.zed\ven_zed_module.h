#pragma once
#include "../sdk/sdk.hpp"
#include "../champion_module.h"
 
class VenZedModule final : public ChampionModule {
public:
    bool Initialize() override;
    void Shutdown() override;
    const char* GetChampionName() override { return "Zed"; }

private:
    static void OnUpdate();
    static void OnRender();
    static void OnProcessSpellCast(const spell_cast& args);
    static void OnCreateObject(game_object* obj);
    static void OnBuffGain(game_object* unit, buff* buff_data);
    static void OnDoCast(const spell_cast& args);
    static void OnNonKillableMinion(game_object* minion);
};