#include "ven_zed.h"
#include "ven_zed_avoider.h"
#include <utility>
#include <map>

void VenZedAvoider::GenerateAvoidList() {
    std::map<std::string, VenZedAvoider *> avoidList;
    avoidList.insert(std::make_pair("infernalguardian", new VenZedAvoider("infernalguardian", SkillshotCircle, "Annie")));
    avoidList.insert(std::make_pair("curseofthesadmummy", new VenZedAvoider("curseofthesadmummy", SelfCast, "Amumu")));
    avoidList.insert(std::make_pair("enchantedcystalarrow", new VenZedAvoider("enchantedcystalarrow", SkillshotLine, "Ashe")));
    avoidList.insert(std::make_pair("aurelionsolr", new VenZedAvoider("aurelionsolr", SkillshotLine, "AurelionSol")));
    avoidList.insert(std::make_pair("azirr", new VenZedAvoider("azirr", SkillshotCircle, "Azir")));
    avoidList.insert(std::make_pair("cassiopeiar", new VenZedAvoider("cassiopeiar", SkillshotCircle, "Cassiopeia")));
    avoidList.insert(std::make_pair("feast", new VenZedAvoider("feast", Targeted, "Chogath")));
    avoidList.insert(std::make_pair("dariusexecute", new VenZedAvoider("dariusexecute", Targeted, "Darius")));
    avoidList.insert(std::make_pair("evelynnr", new VenZedAvoider("evelynnr", SkillshotCircle, "Evelynn")));
    avoidList.insert(std::make_pair("galioidolofdurand", new VenZedAvoider("galioidolofdurand", SelfCast, "Galio")));
    avoidList.insert(std::make_pair("gnarult", new VenZedAvoider("gnarult", SelfCast, "Gnar")));
    avoidList.insert(std::make_pair("garenr", new VenZedAvoider("egarenr", Targeted, "Garen")));
    avoidList.insert(std::make_pair("gravesr", new VenZedAvoider("gravesr", SkillshotLine, "Graves")));
    avoidList.insert(std::make_pair("hecarimult", new VenZedAvoider("hecarimult", SkillshotLine, "Hecarim")));
    avoidList.insert(std::make_pair("illaoir", new VenZedAvoider("illaoir", SelfCast, "Illaoi")));
    avoidList.insert(std::make_pair("jarvanivcataclysm", new VenZedAvoider("jarvanivcataclysm", Targeted, "JarvanIV")));
    avoidList.insert(std::make_pair("blindmonkrkick", new VenZedAvoider("blindmonkrkick", Targeted, "LeeSin")));
    avoidList.insert(std::make_pair("lissandrar", new VenZedAvoider("lissandrar", Targeted, "Lissandra")));
    avoidList.insert(std::make_pair("ufslash", new VenZedAvoider("ufslash", SkillshotCircle, "Malphite")));
    avoidList.insert(std::make_pair("monkeykingspintowin", new VenZedAvoider("monkeykingspintowin", SelfCast, "MonkeyKing")));
    avoidList.insert(std::make_pair("rivenizunablade", new VenZedAvoider("rivenizunablade", SkillshotLine, "Riven")));
    avoidList.insert(std::make_pair("sejuaniglacialprisoncast", new VenZedAvoider("sejuaniglacialprisoncast", SkillshotLine, "Sejuani")));
    avoidList.insert(std::make_pair("shyvanatrasformcast", new VenZedAvoider("shyvanatrasformcast", SkillshotLine, "Shyvana")));
    avoidList.insert(std::make_pair("sonar", new VenZedAvoider("sonar", SkillshotLine, "Sona")));
    avoidList.insert(std::make_pair("syndrar", new VenZedAvoider("syndrar", Targeted, "Syndra")));
    avoidList.insert(std::make_pair("varusr", new VenZedAvoider("varusr", SkillshotLine, "Varus")));
    avoidList.insert(std::make_pair("veigarprimordialburst", new VenZedAvoider("veigarprimordialburst", Targeted, "Veigar")));
    avoidList.insert(std::make_pair("viktorchaosstorm", new VenZedAvoider("viktorchaosstorm", SkillshotCircle, "Viktor")));
    VenZed::AvoidList = avoidList; 
}