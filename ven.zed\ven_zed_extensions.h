#pragma once
#include <functional>
#include <vector>

#include "../sdk/sdk.hpp"

struct ProjectionInfo {
    math::vector2 SegmentPoint;
    bool          IsOnSegment;

    ProjectionInfo(math::vector2 segPoint, bool onSeg) : SegmentPoint(segPoint), IsOnSegment(onSeg) {}
};

struct Action {
    Action(int time, std::function<void()> callback) {
        Time           = time + static_cast<int>(g_sdk->get_clock()->get_game_time() * 1000);
        CallbackObject = callback;
    }

    void Call() {
        CallbackObject();
    }

    int                   Time;
    std::function<void()> CallbackObject;
};

struct ActionIUnit {
    ActionIUnit(int time, game_object* unit, std::function<void(game_object*)> callback) {
        Time           = time + static_cast<int>(g_sdk->get_clock()->get_game_time() * 1000);
        CallbackObject = callback;
        Unit           = unit;
    }

    void Call() {
        CallbackObject(Unit);
    }

    int                               Time;
    std::function<void(game_object*)> CallbackObject;
    game_object*                      Unit;
};

struct ActionPosition {
    ActionPosition(int time, math::vector3 position, std::function<void(math::vector3)> callback) {
        Time           = time + static_cast<int>(g_sdk->get_clock()->get_game_time() * 1000);
        CallbackObject = callback;
        Position       = position;
    }

    void Call() {
        CallbackObject(Position);
    }

    int                                Time;
    std::function<void(math::vector3)> CallbackObject;
    math::vector3                      Position;
};

struct ActionCastedSpell {
    ActionCastedSpell(int time, spell_cast spell, std::function<void(spell_cast)> callback) {
        Time           = time + static_cast<int>(g_sdk->get_clock()->get_game_time() * 1000);
        CallbackObject = callback;
        Spell          = spell;
    }

    void Call() {
        CallbackObject(Spell);
    }

    int                             Time;
    std::function<void(spell_cast)> CallbackObject;
    spell_cast                      Spell;
};

class ActionManager {
   private:
    std::vector<Action>            Actions;
    std::vector<ActionIUnit>       ActionsIUnit;
    std::vector<ActionPosition>    ActionsPosition;
    std::vector<ActionCastedSpell> ActionsCastedSpell;

   public:
    void Add(int Time, std::function<void()> Callback) {
        Actions.emplace_back(Time, Callback);
    }

    void AddIUnit(int Time, game_object* Unit, std::function<void(game_object*)> Callback) {
        ActionsIUnit.emplace_back(Time, Unit, Callback);
    }

    void AddCastedSpell(int Time, spell_cast& spell, std::function<void(spell_cast&)> Callback) {
        ActionsCastedSpell.emplace_back(Time, spell, [Callback](spell_cast s) { Callback(s); });
    }

    void AddPosition(int Time, math::vector3 Position, std::function<void(math::vector3)> Callback) {
        ActionsPosition.emplace_back(Time, Position, Callback);
    }

    void Update() {
        int current_time = static_cast<int>(g_sdk->get_clock()->get_game_time() * 1000);

        // Process and remove completed actions
        Actions.erase(std::remove_if(Actions.begin(),
                          Actions.end(),
                          [current_time](Action& action) {
                              if (current_time >= action.Time) {
                                  action.Call();
                                  return true;
                              }
                              return false;
                          }),
            Actions.end());

        ActionsIUnit.erase(std::remove_if(ActionsIUnit.begin(),
                               ActionsIUnit.end(),
                               [current_time](ActionIUnit& action) {
                                   if (current_time >= action.Time) {
                                       action.Call();
                                       return true;
                                   }
                                   return false;
                               }),
            ActionsIUnit.end());

        ActionsPosition.erase(std::remove_if(ActionsPosition.begin(),
                                  ActionsPosition.end(),
                                  [current_time](ActionPosition& action) {
                                      if (current_time >= action.Time) {
                                          action.Call();
                                          return true;
                                      }
                                      return false;
                                  }),
            ActionsPosition.end());

        ActionsCastedSpell.erase(std::remove_if(ActionsCastedSpell.begin(),
                                     ActionsCastedSpell.end(),
                                     [current_time](ActionCastedSpell& action) {
                                         if (current_time >= action.Time) {
                                             action.Call();
                                             return true;
                                         }
                                         return false;
                                     }),
            ActionsCastedSpell.end());
    }
};

class VenZedExtensions {
   public:
    // Direct VEN API usage - no wrappers
    static bool IsReady(int spell_slot, float time = 0);  // was IsReady(ISpell* spell, float time)
    static bool IsValid(math::vector3 p);                 // was IsValid(Vec3 p)
    static bool IsValid(math::vector2 p);                 // was IsValid(Vec2 p)

    static math::vector2 To2D(math::vector3 p);  // was To2D(Vec3 p)
    static math::vector3 To3D(math::vector2 p);  // was To3D(Vec2 p)
    static math::vector2 Perp(math::vector2 v);  // was Perp(Vec2 v)
    static math::vector3 Perp(math::vector3 v);  // was Perp(Vec3 v)

    static float Dist(game_object* to);                         // was Dist(IUnit* to)
    static float Dist2D(game_object* to);                       // was Dist2D(IUnit* to)
    static float Dist(game_object* from, game_object* to);      // was Dist(IUnit* from, IUnit* to)
    static float Dist2D(game_object* from, game_object* to);    // was Dist2D(IUnit* from, IUnit* to)
    static float Dist2D(math::vector3 from, math::vector3 to);  // was Dist2D(Vec3 from, Vec3 to)
    static float Dist2D(math::vector2 from, math::vector2 to);  // was Dist2D(Vec2 from, Vec2 to)
    static float Dist2D(math::vector2 from, math::vector3 to);  // was Dist2D(Vec2 from, Vec3 to)

    static bool UnderEnemyTurret(math::vector3 pos);   // was UnderEnemyTurret(Vec3 pos)
    static bool UnderEnemyTurret(math::vector2 pos);   // was UnderEnemyTurret(Vec2 pos)
    static bool UnderEnemyTurret(game_object* unit);   // was UnderEnemyTurret(IUnit* unit)
    static bool UnderAllyTurret(game_object* unit);    // was UnderAllyTurret(IUnit* unit)
    static bool IsInEnemyFountain(math::vector3 pos);  // was IsInEnemyFountain(Vec3 pos)
    static bool IsInAllyFountain(math::vector3 pos);   // was IsInAllyFountain(Vec3 pos)

    static void DrawDamageOnChampionHPBar(
        game_object* Hero, double Damage, const char* Text, math::vector4 BarColor);                    // was IUnit* Hero, Vec4 BarColor
    static void   DrawDamageOnChampionHPBar(game_object* Hero, double Damage, math::vector4 BarColor);  // was IUnit* Hero, Vec4 BarColor
    static double AmplifyDamage(game_object* Hero, game_object* Target);                                // was IUnit* Hero, IUnit* Target

    static std::vector<math::vector2> CircleCircleIntersection(math::vector2 center1, math::vector2 center2, float radius1, float radius2);
    static std::vector<game_object*>  GetInRange(
         math::vector2 pos, float range, std::vector<game_object*> group);                     // was Vec2 pos, std::vector<IUnit*> group
    static int CountInRange(game_object* unit, float range, std::vector<game_object*> units);  // was IUnit* unit, std::vector<IUnit*> units
    static int CountInRange(math::vector2 pos, float range, std::vector<game_object*> units);  // was Vec2 pos, std::vector<IUnit*> units
    static ProjectionInfo* ProjectOn(
        math::vector2 point, math::vector2 start, math::vector2 end);  // was ProjectOn(Vec2 point, Vec2 start, Vec2 end)

    // Action manager instance
    static ActionManager Actions;
};

// Inline implementations for simple functions
inline bool VenZedExtensions::IsReady(int spell_slot, float time) {  // was IsReady(ISpell* spell, float time)
    return sdk::common->spell->is_spell_ready(spell_slot, time);     // was spell->IsReady(time)
}

inline bool VenZedExtensions::IsValid(math::vector3 p) {
    return p.x != 0 && p.z != 0;
}

inline bool VenZedExtensions::IsValid(math::vector2 p) {
    return p.x != 0 && p.y != 0;
}

inline math::vector2 VenZedExtensions::To2D(math::vector3 p) {
    return {p.x, p.z};
}

inline math::vector3 VenZedExtensions::To3D(math::vector2 p) {
    return {p.x, 0, p.y};
}

inline math::vector2 VenZedExtensions::Perp(math::vector2 v) {
    return {-v.y, v.x};
}

inline math::vector3 VenZedExtensions::Perp(math::vector3 v) {
    return {-v.z, v.y, v.x};
}

inline float VenZedExtensions::Dist(game_object* to) {  // was Dist(IUnit* to)
    auto player = g_sdk->get_local_player();
    return player->get_position().distance(to->get_position());  // was player->GetPosition().distance(to->GetPosition())
}

inline float VenZedExtensions::Dist2D(game_object* to) {  // was Dist2D(IUnit* to)
    auto player     = g_sdk->get_local_player();
    auto player_pos = To2D(player->get_position());  // was To2D(player->GetPosition())
    auto to_pos     = To2D(to->get_position());      // was To2D(to->GetPosition())
    return Dist2D(player_pos, to_pos);
}

inline float VenZedExtensions::Dist(game_object* from, game_object* to) {  // was Dist(IUnit* from, IUnit* to)
    return from->get_position().distance(to->get_position());              // was from->GetPosition().distance(to->GetPosition())
}

inline float VenZedExtensions::Dist2D(game_object* from, game_object* to) {  // was Dist2D(IUnit* from, IUnit* to)
    auto from_pos = To2D(from->get_position());                              // was To2D(from->GetPosition())
    auto to_pos   = To2D(to->get_position());                                // was To2D(to->GetPosition())
    return Dist2D(from_pos, to_pos);
}

inline float VenZedExtensions::Dist2D(math::vector3 from, math::vector3 to) {  // was Dist2D(Vec3 from, Vec3 to)
    auto from_2d = To2D(from);
    auto to_2d   = To2D(to);
    return Dist2D(from_2d, to_2d);
}

inline float VenZedExtensions::Dist2D(math::vector2 from, math::vector2 to) {  // was Dist2D(Vec2 from, Vec2 to)
    float dx = to.x - from.x;
    float dy = to.y - from.y;
    return std::sqrt(dx * dx + dy * dy);
}

inline float VenZedExtensions::Dist2D(math::vector2 from, math::vector3 to) {  // was Dist2D(Vec2 from, Vec3 to)
    auto to_2d = To2D(to);
    return Dist2D(from, to_2d);
}

inline bool VenZedExtensions::UnderEnemyTurret(game_object* unit) {  // was UnderEnemyTurret(IUnit* unit)
    return UnderEnemyTurret(unit->get_position());                   // was unit->GetPosition()
}

inline void VenZedExtensions::DrawDamageOnChampionHPBar(
    game_object* unit, double damage, math::vector4 color) {  // was IUnit* unit, Vec4 color
    // Use VEN renderer directly
    g_sdk->get_renderer()->add_damage_indicator(unit, static_cast<float>(damage));  // was custom drawing code
}