#pragma once
#include <map>
#include <string>

#include "../sdk/sdk.hpp"

class VenZedMenu {
   public:
    explicit VenZedMenu(menu_category* parent_menu);
    ~VenZedMenu();

    menu_category* MainMenu;

    std::map<uint32_t, menu_item*> BlackListRTargets;
    std::map<std::string, menu_item*> SpellsToAvoid;

    menu_item* ComboKey;
    menu_item* HarassKey;
    menu_item* ClearKey;
    menu_item* FleeKey;

    menu_item* UseComboQ;
    menu_item* ExtendedQCombo;
    menu_item* UseComboW;
    menu_item* GapcloseAfterR;
    menu_item* SwapForKill;
    menu_item* UseComboE;
    menu_item* UseComboR;
    menu_item* AutoR;
    menu_item* AlwaysRSelected;
    menu_item* UltMode;
    menu_item* ShadowPlacement;
    menu_item* AssassinRange;
    menu_item* SwapRIfDead;
    menu_item* LaughIfDead;
    menu_item* UseBlackList;

    menu_item* UseHarassQ;
    menu_item* ExtendedQHarass;
    menu_item* UseHarassW;
    menu_item* UseHarassWPF;
    menu_item* UseHarassE;

    menu_item* UseJungleQ;
    menu_item* UseJungleW;
    menu_item* DontWJungleNearEnemy;
    menu_item* UseJungleE;

    menu_item* LastHitQ;
    menu_item* LastHitQCount;
    menu_item* LastHitQUnkillable;
    menu_item* LastHitE;
    menu_item* LastHitECount;
    menu_item* LastHitEUnkillable;

    menu_item* UseFleeW;

    menu_item* MinimumHarassEnergy;
    menu_item* MinimumClearEnergy;
    menu_item* MinimumLastHitEnergy;

    menu_item* UseItemsCombo;
    menu_item* UseIgnite;
    menu_item* LowFPSMode;
    menu_item* AutoEUnitInRage;
    menu_item* AutoEUnitInRagePct;
    menu_item* JungleOrderPriority;

    menu_item* DrawQ;
    menu_item* DrawW;
    menu_item* DrawE;
    menu_item* DrawR;
    menu_item* DrawAssassinRange;
    menu_item* DrawComboDamage;

    menu_item* UseRAvoider;

    menu_item* DebugDamage;
    menu_item* DrawPredictedShadow;
    menu_item* DebugPathfinding;
};