#pragma once
#include <map>
#include <string>
#include <vector>

#include "../sdk/sdk.hpp"
#include "ven_zed_avoider.h"
#include "ven_zed_extensions.h"
#include "ven_zed_menu.h"
#include "ven_zed_modes.h"

class VenZed {
   public:
    static game_object*      Player;
    static VenZedMenu*       Menu;
    static VenZedModes*      Modes;
    static VenZedExtensions* Ex;

    static const int Q_SLOT = 0;
    static const int W_SLOT = 1;
    static const int E_SLOT = 2;
    static const int R_SLOT = 3;
    static int       IGNITE_SLOT;

    static constexpr float Q_RANGE = 900.f;
    static constexpr float W_RANGE = 700.f;
    static constexpr float E_RANGE = 290.f;
    static constexpr float R_RANGE = 625.f;

    static const int TIAMAT_ID      = 3077;
    static const int HYDRA_ID       = 3074;
    static const int TITANIC_ID     = 3748;
    static const int YOUMUUS_ID     = 3142;
    static const int BILGEWATER_ID  = 3144;
    static const int BOTRK_ID       = 3153;
    static const int EDGEOFNIGHT_ID = 3814;

    static std::map<float, game_object*>         Marked;
    static std::map<float, game_object*>         Shadows;
    static std::map<float, math::vector3>        WPositions;
    static std::map<std::string, float>          Ticks;
    static std::map<std::string, VenZedAvoider*> AvoidList;

    static void UseQ(game_object* unit, bool harass = false);
    static void UseQEx(game_object* unit, bool jungle);
    static void UseW(game_object* unit, bool harass);
    static void UseWEx(game_object* unit, bool jungle);
    static void UseE(game_object* unit, bool harass);
    static void UseEEx(game_object* unit, bool jungle);
    static void UseR(game_object* unit, bool engage, bool killsteal = false);

    static bool SoloQ(math::vector3 sourcepos, game_object* unit);
    static void GetMaxWPositions(game_object* unit, math::vector3& wpos);
    static void GetBestWPosition(game_object* unit, math::vector3& wpos, bool harass = false, bool onupdate = false);

    static void OnBoot();
    static void OnShutdown();
    static bool LethalTarget(game_object* unit);
    static bool CompareLowHealth(game_object* a, game_object* b);
    static bool CompareMaxHealth(game_object* a, game_object* b);
    static bool CompareDistanceToCursor(game_object* a, game_object* b);
    static bool GetPriorityJungleTarget(game_object* a, game_object* b);
    static void CanUlt(game_object* unit, bool& engage);
    static bool Recent(std::string name, float time = 0.f);
    static bool CanShadowCPA(game_object* unit, bool harass);

    static bool CanSwap(int spell_slot);
    static bool HasDeathMark(game_object* unit);
    static bool WShadowExists(bool strict = false);
    static bool RShadowExists(bool strict = false);

    static game_object* RShadow();
    static game_object* WShadow();

    static double QDmg(game_object* source, game_object* unit, double& energy);
    static double EDmg(game_object* unit, double& energy);
    static double RDmg(game_object* unit, float predictedDmg);
    static double AADmg(game_object* unit, float healthmod);
    static double CDmg(game_object* unit, double& energy);
};

inline void VenZed::OnBoot() {
    Ticks["ZedQ"]   = 0;
    Ticks["ZedW"]   = 0;
    Ticks["ZedW2"]  = 0;
    Ticks["ZedE"]   = 0;
    Ticks["ZedR"]   = 0;
    Ticks["ZedR2"]  = 0;
    Ticks["Engage"] = 0;
    Ticks["Lethal"] = 0;

    auto spell_d = Player->get_spell(4);
    if (spell_d && spell_d->get_name().find("SummonerDot") != std::string::npos) {
        IGNITE_SLOT = 4;
    }
    auto spell_f = Player->get_spell(5);
    if (spell_f && spell_f->get_name().find("SummonerDot") != std::string::npos) {
        IGNITE_SLOT = 5;
    }
}

inline void VenZed::OnShutdown() {
    WPositions.clear();
    Shadows.clear();
    Marked.clear();
    Ticks.clear();
}

inline bool VenZed::LethalTarget(game_object* unit) {
    double energy;
    return CDmg(unit, energy) / 1.65 >= unit->get_hp();
}

inline bool VenZed::CompareLowHealth(game_object* a, game_object* b) {
    return a->get_hp_percent() < b->get_hp_percent();
}

inline bool VenZed::CompareMaxHealth(game_object* a, game_object* b) {
    return a->get_max_hp() > b->get_max_hp();
}

inline bool VenZed::CompareDistanceToCursor(game_object* a, game_object* b) {
    auto cursor_pos = g_sdk->hud_manager->get_cursor_position();
    float dist_a = Ex->Dist2D(a->get_server_position(), cursor_pos);
    float dist_b = Ex->Dist2D(b->get_server_position(), cursor_pos);
    return dist_a < dist_b;
}

inline bool VenZed::GetPriorityJungleTarget(game_object* a, game_object* b) {
    switch (Menu->JungleOrderPriority->get_int()) {
        case 0:
            return CompareLowHealth(a, b);
        case 1:
            return CompareMaxHealth(a, b);
        case 2:
            return CompareDistanceToCursor(a, b);
    }
    return false;
}

inline bool VenZed::CanShadowCPA(game_object* unit, bool harass) {
    if (harass) {
        if (Menu->UseHarassWPF->get_int() == 1) {
            return true;
        }
        if (Menu->UseHarassWPF->get_int() == 2) {
            if (Ex->Dist2D(unit) > Q_RANGE + 300) {
                return true;
            }
        }
    }
    return false;
}