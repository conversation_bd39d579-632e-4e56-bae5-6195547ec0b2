#include "ven_zed_menu.h"
#include "ven_zed.h"

VenZedMenu::VenZedMenu(menu_category* parent_menu) {
    MainMenu = parent_menu;

    auto key_menu = MainMenu->add_category("keys", "Keys");
    ComboKey = key_menu->add_key_bind("combokey", "Combo", 32, false, false);
    HarassKey = key_menu->add_key_bind("harasskey", "Harass", 'G', false, false);
    ClearKey = key_menu->add_key_bind("clearkey", "Clear/Last Hit", 'V', false, false);
    FleeKey = key_menu->add_key_bind("fleekey", "Flee", 'A', false, false);

    auto combo_menu = MainMenu->add_category("combo", "Combo");
    UseComboQ = combo_menu->add_checkbox("use_q", "Use Q", true);
    ExtendedQCombo = combo_menu->add_checkbox("ext_q", "Use Extended Q", true);
    UseComboW = combo_menu->add_checkbox("use_w", "Use W", true);
    GapcloseAfterR = combo_menu->add_checkbox("gap_w_r", "Gapclose W on Marked Target", false);
    SwapForKill = combo_menu->add_checkbox("swap_kill", "Swap W for Kill", true);
    UseComboE = combo_menu->add_checkbox("use_e", "Use E", true);
    UseComboR = combo_menu->add_checkbox("use_r", "Use R", true);
    AutoR = combo_menu->add_checkbox("ks_r", "Killsteal R", true);
    AlwaysRSelected = combo_menu->add_checkbox("always_r", "Always R on Focus Target", true);
    UltMode = combo_menu->add_combo("ult_mode", "R Mode", { "Only Kill", "Duel" }, 0);
    ShadowPlacement = combo_menu->add_combo("shadow_place", "Shadow Combo", { "Line", "Triangle", "Pathfinder", "None" }, 0);
    AssassinRange = combo_menu->add_slider("assassin_range", "Assassination Range", 1200, 625, 2500);
    SwapRIfDead = combo_menu->add_checkbox("swap_r_dead", "Swap R if in Danger", true);
    LaughIfDead = combo_menu->add_checkbox("laugh_dead", "Laugh on Kill with R", true);

    auto r_blacklist_menu = combo_menu->add_category("r_blacklist", "R Blacklist");
    UseBlackList = r_blacklist_menu->add_checkbox("use_blacklist", "Enable R Blacklist", true);
    for (auto&& hero : g_sdk->object_manager->get_heroes()) {
        if (hero && hero->is_enemy()) {
            BlackListRTargets[hero->get_network_id()] = r_blacklist_menu->add_checkbox(
                "dont_r_" + hero->get_char_name(), "Don't R on " + hero->get_char_name(), false);
        }
    }

    auto harass_menu = MainMenu->add_category("harass", "Harass");
    UseHarassQ = harass_menu->add_checkbox("use_q", "Use Q", true);
    ExtendedQHarass = harass_menu->add_checkbox("ext_q", "Use Extended Q", false);
    UseHarassW = harass_menu->add_checkbox("use_w", "Use W", true);
    UseHarassWPF = harass_menu->add_combo("w_pf", "W Harass Mode", { "W->E->Q", "Pathfinder", "Auto" }, 0);
    UseHarassE = harass_menu->add_checkbox("use_e", "Use E", true);

    auto jungle_menu = MainMenu->add_category("jungle", "Jungle Clear");
    UseJungleQ = jungle_menu->add_checkbox("use_q", "Use Q", true);
    UseJungleW = jungle_menu->add_checkbox("use_w", "Use W", true);
    DontWJungleNearEnemy = jungle_menu->add_checkbox("no_w_enemy", "Don't W near Enemies", true);
    UseJungleE = jungle_menu->add_checkbox("use_e", "Use E", true);

    auto lasthit_menu = MainMenu->add_category("lasthit", "Last Hit");
    LastHitQ = lasthit_menu->add_checkbox("use_q", "Use Q", true);
    LastHitQCount = lasthit_menu->add_slider("q_count", "Minions to Q", 2, 1, 6);
    LastHitQUnkillable = lasthit_menu->add_checkbox("q_unkillable", "Q on Unkillable Minion", true);
    LastHitE = lasthit_menu->add_checkbox("use_e", "Use E", true);
    LastHitECount = lasthit_menu->add_slider("e_count", "Minions to E", 2, 1, 6);
    LastHitEUnkillable = lasthit_menu->add_checkbox("e_unkillable", "E on Unkillable Minion", true);

    auto flee_menu = MainMenu->add_category("flee", "Flee");
    UseFleeW = flee_menu->add_checkbox("use_w", "Use W", true);

    auto energy_menu = MainMenu->add_category("energy", "Energy Manager");
    MinimumHarassEnergy = energy_menu->add_slider("min_harass", "Min Harass Energy", 100, 0, 200);
    MinimumClearEnergy = energy_menu->add_slider("min_clear", "Min Clear Energy", 100, 0, 200);
    MinimumLastHitEnergy = energy_menu->add_slider("min_lh", "Min Last Hit Energy", 145, 0, 200);

    auto mechanics_menu = MainMenu->add_category("mechanics", "Mechanics");
    UseItemsCombo = mechanics_menu->add_checkbox("use_items", "Use Items in Combo", true);
    UseIgnite = mechanics_menu->add_checkbox("use_ignite", "Use Ignite if Killable", true);
    LowFPSMode = mechanics_menu->add_checkbox("low_fps", "Low FPS Mode (less logic)", false);
    AutoEUnitInRage = mechanics_menu->add_checkbox("auto_e", "Auto E on Enemies", true);
    AutoEUnitInRagePct = mechanics_menu->add_slider("auto_e_energy", "if Energy >", 100, 0, 200);
    JungleOrderPriority = mechanics_menu->add_combo("jungle_prio", "Jungle Target Priority", { "Low Health", "Max Health", "Closest to Cursor" }, 1);

    auto draw_menu = MainMenu->add_category("drawings", "Drawings");
    DrawQ = draw_menu->add_checkbox("draw_q", "Draw Q Range", true);
    DrawW = draw_menu->add_checkbox("draw_w", "Draw W Range", true);
    DrawE = draw_menu->add_checkbox("draw_e", "Draw E Range", false);
    DrawR = draw_menu->add_checkbox("draw_r", "Draw R Range", true);
    DrawAssassinRange = draw_menu->add_checkbox("draw_assassin", "Draw Assassin Range", true);
    DrawComboDamage = draw_menu->add_checkbox("draw_dmg", "Draw Combo Damage", true);

    auto avoider_menu = MainMenu->add_category("avoider", "R Avoider");
    UseRAvoider = avoider_menu->add_checkbox("use_avoider", "Enable R Avoider", true);
    for (auto&& hero : g_sdk->object_manager->get_heroes()) {
        if (hero && hero->is_enemy()) {
            for (auto& i : VenZed::AvoidList) {
                if (hero->get_char_name() == i.second->ChampName) {
                    auto uniqueHero = "- " + i.second->ChampName + " R";
                    SpellsToAvoid[i.first] = avoider_menu->add_checkbox(
                        "avoid_" + i.first, uniqueHero, true);
                }
            }
        }
    }

    auto debug_menu = MainMenu->add_category("debug", "Debug");
    DebugDamage = debug_menu->add_checkbox("debug_dmg", "Debug Damage Values", false);
    DrawPredictedShadow = debug_menu->add_checkbox("debug_shadow", "Debug Shadow Position", false);
    DebugPathfinding = debug_menu->add_checkbox("debug_path", "Debug Shadow Pathfinding", false);
}

VenZedMenu::~VenZedMenu() {
    // VEN menu handles its own cleanup
}