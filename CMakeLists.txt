cmake_minimum_required(VERSION 3.16)
project(ven-solutions)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Add include directories
include_directories(.)
include_directories(sdk)

# Collect all source files
file(GLOB_RECURSE SOURCES 
    "main.cpp"
    "ven.zed/*.cpp"
    "ven.zed/*.h"
    "sdk/*.hpp"
    "champion_module.h"
)

# Create the library
add_library(ven-solutions SHARED ${SOURCES})

# Set output directory
set_target_properties(ven-solutions PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
)

# Windows specific settings
if(WIN32)
    target_compile_definitions(ven-solutions PRIVATE WIN32_LEAN_AND_MEAN)
    target_link_libraries(ven-solutions PRIVATE kernel32 user32)
endif()
