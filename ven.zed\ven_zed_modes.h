#pragma once
#include "../sdk/sdk.hpp"

class VenZedModes {
   public:
    void OnUpdate();
    void Combo();
    void Harass();
    void Flee();
    void WaveClear();
    void Jungling();
    void OnSpellCast(const spell_cast& args);  // was OnSpellCast(const CastedSpell& args)
    void OnRender();
    void OnCreateObj(game_object* unit);                // was OnCreateObj(IUnit* unit)
    void OnBuffAdd(game_object* unit, void* buffdata);  // was OnBuffAdd(IUnit* unit, void* buffdata)
    void OnDoCast(const spell_cast& args);              // was OnDoCast(const CastedSpell& args)
    void Auto();
    void OnNonKillableMinion(game_object* unit);  // was OnNonKillableMinion(IUnit* unit)

   private:
    // Helper functions for VEN integration
    game_object*              GetComboTarget();
    game_object*              GetHarassTarget();
    std::vector<game_object*> GetEnemyHeroes();
    std::vector<game_object*> GetEnemyMinions();
    std::vector<game_object*> GetJungleMonsters();
    bool                      CanCastSpell(game_object* target, float cast_time = 0.25f);
};

// Inline implementations
inline void VenZedModes::OnUpdate() {
    // Direct VEN orbwalker mode detection
    if (sdk::orbwalker->combo()) {  // was custom key detection
        Combo();
    } else if (sdk::orbwalker->harass()) {  // was custom key detection
        Harass();
    } else if (sdk::orbwalker->clear()) {  // was custom key detection
        WaveClear();
    } else if (sdk::orbwalker->flee()) {  // was custom key detection
        Flee();
    }

    Auto();  // Always run auto logic

    // Update action manager
    VenZedExtensions::Actions.Update();
}

inline game_object* VenZedModes::GetComboTarget() {
    // Use VEN target selector directly
    return sdk::target_selector->get_hero_target();  // was GTargetSelector->GetTarget()
}

inline game_object* VenZedModes::GetHarassTarget() {
    // Use VEN target selector with custom filter for harass
    return sdk::target_selector->get_hero_target([](game_object* target) {
        // Add harass-specific filtering logic here
        return target && target->is_hero() && !target->is_dead();
    });
}

inline std::vector<game_object*> VenZedModes::GetEnemyHeroes() {
    std::vector<game_object*> enemies;
    auto                      heroes = g_sdk->object_manager->get_heroes();  // was GEntityList->GetAllHeroes()

    for (auto hero : heroes) {
        if (hero && hero->is_hero() && !hero->is_dead() &&
            hero->get_team_id() != g_sdk->object_manager->get_local_player()->get_team_id()) {  // was hero->GetTeam() != Player->GetTeam()
            enemies.push_back(hero);
        }
    }

    return enemies;
}

inline std::vector<game_object*> VenZedModes::GetEnemyMinions() {
    std::vector<game_object*> minions;
    auto                      all_minions = g_sdk->object_manager->get_minions();  // was GEntityList->GetAllMinions()

    for (auto minion : all_minions) {
        if (minion && minion->is_minion() && !minion->is_dead() &&
            minion->get_team_id() !=
                g_sdk->object_manager->get_local_player()->get_team_id()) {  // was minion->GetTeam() != Player->GetTeam()
            minions.push_back(minion);
        }
    }

    return minions;
}

inline std::vector<game_object*> VenZedModes::GetJungleMonsters() {
    std::vector<game_object*> monsters;
    auto                      all_monsters = g_sdk->object_manager->get_monsters();  // was GEntityList->GetJungle()

    for (auto monster : all_monsters) {
        if (monster && monster->is_monster() && !monster->is_dead()) {
            monsters.push_back(monster);
        }
    }

    return monsters;
}

inline bool VenZedModes::CanCastSpell(game_object* target, float cast_time) {
    // Use VEN orbwalker spell weaving
    return sdk::orbwalker->can_spell(target, cast_time);  // was custom spell weaving logic
}