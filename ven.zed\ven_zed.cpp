#include "ven_zed.h"

game_object*      VenZed::Player;
VenZedMenu*       VenZed::Menu;
VenZedModes*      VenZed::Modes;
VenZedExtensions* VenZed::Ex;

int VenZed::IGNITE_SLOT = -1;

std::map<float, game_object*>         VenZed::Marked;
std::map<float, game_object*>         VenZed::Shadows;
std::map<float, math::vector3>        VenZed::WPositions;
std::map<std::string, float>          VenZed::Ticks;
std::map<std::string, VenZedAvoider*> VenZed::AvoidList;

void VenZed::UseQ(game_object* unit, bool harass) {
    if (!unit || !unit->is_valid() || !Ex->IsReady(Q_SLOT, 0)) return;

    if (Ex->Dist2D(unit) <= Q_RANGE && unit->is_hero()) {
        if ((harass && Menu->UseHarassQ->get_bool()) || (!harass && Menu->UseComboQ->get_bool())) {
            pred_sdk::spell_data spell_data;
            spell_data.spell_type = pred_sdk::spell_type::linear;
            spell_data.range = Q_RANGE;
            spell_data.radius = 50.f;
            spell_data.delay = 0.25f;
            spell_data.projectile_speed = 1700.f;
            spell_data.forbidden_collisions = { pred_sdk::collision_type::unit, pred_sdk::collision_type::yasuo_wall };
            auto pred = sdk::prediction->predict(unit, spell_data);
            if (pred.is_valid && pred.hitchance >= pred_sdk::hitchance::medium) {
                sdk::common->cast_spell(Q_SLOT, pred.cast_position);
            }
        }
    }

    if (Ex->Dist2D(unit) > Q_RANGE && unit->is_hero()) {
        bool use_ext_q = (harass && Menu->ExtendedQHarass->get_bool()) || (!harass && Menu->ExtendedQCombo->get_bool());
        if (!use_ext_q) return;

        for (auto& z : Shadows) {
            auto shadow = z.second;
            if (Ex->Dist2D(shadow, unit) <= Q_RANGE + unit->get_bounding_radius()) {
                pred_sdk::spell_data spell_data;
                spell_data.source_position = shadow->get_server_position();
                spell_data.spell_type = pred_sdk::spell_type::linear;
                spell_data.range = Q_RANGE;
                spell_data.radius = 50.f;
                spell_data.delay = 0.25f;
                spell_data.projectile_speed = 1700.f;
                spell_data.forbidden_collisions = { pred_sdk::collision_type::unit, pred_sdk::collision_type::yasuo_wall };
                auto pred = sdk::prediction->predict(unit, spell_data);
                if (pred.is_valid && pred.hitchance >= pred_sdk::hitchance::medium) {
                    sdk::common->cast_spell(Q_SLOT, pred.cast_position);
                }
            }
        }
    }
}

void VenZed::UseW(game_object* unit, bool harass) {
    if (!unit || !unit->is_valid() || !Ex->IsReady(W_SLOT, 0)) return;

    // Gapclose after R
    if (WShadowExists() && !Marked.empty() && Menu->GapcloseAfterR->get_bool()) {
        if (CanSwap(W_SLOT) && Ex->Dist2D(unit) > Player->get_attack_range() + 100) {
            auto w_shadow = WShadow();
            if (w_shadow && Ex->Dist2D(w_shadow->get_server_position(), unit->get_server_position()) < Ex->Dist2D(unit)) {
                if (Ex->Dist2D(w_shadow->get_server_position(), Player->get_server_position()) > E_RANGE + 55) {
                    if (!Recent("Lethal", 2000)) {
                        sdk::common->cast_spell(W_SLOT);
                    }
                }
            }
        }
    }
    if (WShadowExists()) return;

    math::vector3 castposition = unit->get_server_position();
    GetBestWPosition(unit, castposition, harass);

    if (Menu->DebugPathfinding->get_bool()) {
        g_sdk->renderer->add_circle_3d(castposition, 50, { 255, 255, 255, 255 }, 10.f);
    }

    if (Ex->Dist2D(unit) <= W_RANGE * 2) {
        bool can_use_w = (harass && Menu->UseHarassW->get_bool() && Player->get_mana() >= Menu->MinimumHarassEnergy->get_int()) ||
                         (!harass && Menu->UseComboW->get_bool());

        if (can_use_w) {
            bool can_hit_with_e = Ex->Dist2D(unit, castposition) <= E_RANGE;
            bool use_ext_q = (harass && Menu->ExtendedQHarass->get_bool()) || (!harass && Menu->ExtendedQCombo->get_bool());

            if (can_hit_with_e || use_ext_q) {
                if (Ex->IsReady(Q_SLOT, 0) && Player->get_mana() >= 70 + 40) { // Q + W cost
                    sdk::common->cast_spell(W_SLOT, castposition);
                }
                if (Ex->IsReady(E_SLOT, 0) && Player->get_mana() >= 50 + 40) { // E + W cost
                    sdk::common->cast_spell(W_SLOT, castposition);
                }
            }
        }
    }
}

void VenZed::UseE(game_object* unit, bool harass) {
    if (!unit || !unit->is_valid() || !Ex->IsReady(E_SLOT, 0)) return;

    if (Player->get_mana() >= 50) {
        bool can_use_e = (harass && Menu->UseHarassE->get_bool()) || (!harass && Menu->UseComboE->get_bool());
        if (!can_use_e) return;

        if (Ex->Dist2D(unit) <= E_RANGE + 55) {
            sdk::common->cast_spell(E_SLOT);
            return;
        }

        for (auto& pair : Shadows) {
            if (Ex->Dist2D(unit, pair.second) <= E_RANGE + unit->get_bounding_radius()) {
                sdk::common->cast_spell(E_SLOT);
                return;
            }
        }
    }
}

void VenZed::UseR(game_object* unit, bool engage, bool killsteal) {
    if (unit && unit->is_valid() && Ex->IsReady(R_SLOT, 0)) {
        double energy = 0;
        if (!Ex->IsKeyDown(Menu->ComboKey) && (killsteal && CDmg(unit, energy) < unit->get_hp())) {
            return;
        }

        if (engage && Menu->UseItemsCombo->get_bool()) {
            if (sdk::common->item->is_item_ready(YOUMUUS_ID) && Ex->Dist2D(unit) <= Menu->AssassinRange->get_int()) {
                sdk::common->item->use_item(YOUMUUS_ID);
            }
            if (sdk::common->item->is_item_ready(BOTRK_ID) && Ex->Dist2D(unit) <= 550) {
                sdk::common->item->use_item(BOTRK_ID, unit);
            }
            if (sdk::common->item->is_item_ready(BILGEWATER_ID) && Ex->Dist2D(unit) <= 550) {
                sdk::common->item->use_item(BILGEWATER_ID, unit);
            }
        }

        if (engage && Menu->UseIgnite->get_bool() && IGNITE_SLOT != -1 && Ex->IsReady(IGNITE_SLOT, 0)) {
            if (Ex->Dist2D(unit) <= 600) {
                sdk::common->cast_spell(IGNITE_SLOT, unit);
            }
        }

        if (engage && CanSwap(W_SLOT) && !HasDeathMark(unit) && !killsteal) {
            auto w_shadow = WShadow();
            if (w_shadow && Ex->Dist2D(w_shadow, unit) <= Player->get_attack_range() + Player->get_bounding_radius()) {
                if (Menu->SwapForKill->get_bool() && CDmg(unit, energy) >= unit->get_hp()) {
                    sdk::common->cast_spell(W_SLOT);
                }
            }
        }

        if (engage && !RShadowExists() && Menu->UseComboR->get_bool()) {
            if (Ex->Dist2D(unit) <= R_RANGE) {
                sdk::common->cast_spell(R_SLOT, unit);
            } else { // Swap to W to get in range for R
                auto w_shadow = WShadow();
                if (w_shadow && CanSwap(W_SLOT) && Ex->Dist2D(w_shadow, unit) <= R_RANGE) {
                    if (Menu->SwapForKill->get_bool() && CDmg(unit, energy) >= unit->get_hp()) {
                        sdk::common->cast_spell(W_SLOT);
                    }
                }
            }
        }
    }
}

void VenZed::CanUlt(game_object* unit, bool& engage) {
    engage = false;
    if (!unit || !unit->is_hero()) return;

    if (Recent("Engage", 500)) {
        engage = true;
        return;
    }

    if (HasDeathMark(unit)) return;

    if (Menu->BlackListRTargets.count(unit->get_network_id()) == 0) {
        // This should be handled by menu creation, but as a fallback:
        return;
    }

    if (Ex->IsReady(R_SLOT, 0)) {
        auto focus = sdk::target_selector->get_forced_target();
        if (Menu->AlwaysRSelected->get_bool() && focus && focus->get_network_id() == unit->get_network_id()) {
            engage = true;
            Ticks["Engage"] = g_sdk->clock_facade->get_game_time() * 1000;
            return;
        }

        if (Menu->UseBlackList->get_bool() && Menu->BlackListRTargets[unit->get_network_id()]->get_bool()) {
            return;
        }

        double energy = 0;
        if (CDmg(unit, energy) >= unit->get_hp()) {
            engage = true;
            Ticks["Engage"] = g_sdk->clock_facade->get_game_time() * 1000;
        }
    }
}

bool VenZed::HasDeathMark(game_object* unit) {
    return unit && unit->has_buff("zedrdeathmark");
}

bool VenZed::WShadowExists(bool strict) {
    if (!strict && Recent("ZedW", 500 + g_sdk->net_client->get_ping())) return true;
    return WShadow() != nullptr;
}

bool VenZed::RShadowExists(bool strict) {
    if (!strict && Recent("ZedR", 500 + g_sdk->net_client->get_ping())) return true;
    return RShadow() != nullptr;
}

game_object* VenZed::WShadow() {
    for (auto& pair : Shadows) {
        auto shadow = pair.second;
        if (shadow && shadow->is_valid() && shadow->has_buff("zedwshadowbuff")) {
            return shadow;
        }
    }
    return nullptr;
}

game_object* VenZed::RShadow() {
    for (auto& pair : Shadows) {
        auto shadow = pair.second;
        if (shadow && shadow->is_valid() && shadow->has_buff("zedrshadowbuff")) {
            return shadow;
        }
    }
    return nullptr;
}

void VenZed::GetBestWPosition(game_object* unit, math::vector3& wpos, bool harass, bool onupdate) {
    if (Menu->ShadowPlacement->get_int() == 3) { // None
        wpos = unit->get_server_position();
        return;
    }

    if (Recent("ZedR", 1500) || onupdate || CanShadowCPA(unit, true)) {
        if (Menu->ShadowPlacement->get_int() == 2 || CanShadowCPA(unit, true)) { // Pathfinder
            GetMaxWPositions(unit, wpos);
            return;
        }

        auto r_shadow = RShadow();
        auto source_pos = r_shadow ? r_shadow->get_server_position() : Player->get_server_position();

        if (Menu->ShadowPlacement->get_int() == 0) { // Line
            wpos = source_pos.extend(unit->get_server_position(), W_RANGE + 200);
        } else if (Menu->ShadowPlacement->get_int() == 1) { // Triangle
            auto v = Ex->CircleCircleIntersection(Ex->To2D(source_pos), Ex->To2D(Player->get_server_position()), 550, 450);
            wpos = v.size() > 0 ? Ex->To3D(v.front()) : source_pos.extend(unit->get_server_position(), W_RANGE + 200);
        }
    } else {
        wpos = Player->get_server_position().extend(unit->get_server_position(), W_RANGE);
    }
}

double VenZed::QDmg(game_object* source, game_object* unit, double& energy) {
    if (!source || !unit) return 0.0;
    int level = Player->get_spell(Q_SLOT)->get_level();
    if (level == 0) return 0.0;

    double base_dmg = std::vector<double>{ 70, 105, 140, 175, 210 }[level - 1];
    double bonus_ad = Player->get_bonus_attack_damage();
    double raw_damage = base_dmg + (0.9 * bonus_ad);

    double damage_multiplier = 100.0 / (100.0 + unit->get_armor());
    return raw_damage * damage_multiplier;
}

double VenZed::EDmg(game_object* unit, double& energy) {
    if (!unit) return 0.0;
    int level = Player->get_spell(E_SLOT)->get_level();
    if (level == 0) return 0.0;

    double base_dmg = std::vector<double>{ 65, 90, 115, 140, 165 }[level - 1];
    double bonus_ad = Player->get_bonus_attack_damage();
    double raw_damage = base_dmg + (0.8 * bonus_ad);

    double damage_multiplier = 100.0 / (100.0 + unit->get_armor());
    return raw_damage * damage_multiplier;
}

double VenZed::RDmg(game_object* unit, float predictedDmg) {
    if (!unit || !Ex->IsReady(R_SLOT, 0)) return 0.0;
    int level = Player->get_spell(R_SLOT)->get_level();
    if (level == 0) return 0.0;

    double percent = std::vector<double>{ 0.25, 0.40, 0.55 }[level - 1];
    double raw_damage = (Player->get_total_attack_damage() * 1.0) + (predictedDmg * percent);

    double damage_multiplier = 100.0 / (100.0 + unit->get_armor());
    return raw_damage * damage_multiplier;
}

double VenZed::AADmg(game_object* unit, float healthmod) {
    if (!unit) return 0.0;
    double raw_damage = sdk::damage->get_auto_attack_damage(Player, unit);

    if (!unit->has_buff("zedpassivecd") && unit->get_hp_percent() <= 50) {
        int p_level = std::min(18, (int)Player->get_level());
        double p_percent = std::vector<double>{ 0.06, 0.08, 0.10 }[p_level >= 13 ? 2 : (p_level >= 7 ? 1 : 0)];
        raw_damage += (unit->get_max_hp() * p_percent); // This part is magic damage
    }
    return raw_damage;
}

double VenZed::CDmg(game_object* unit, double& energy) {
    if (!unit) return 0.0;
    energy = 0;
    double dmg = 0;

    int shurikens = 1 + (WShadowExists() ? 1 : 0) + (RShadowExists() ? 1 : 0);
    double q_dmg = QDmg(Player, unit, energy);
    if (shurikens > 1) {
        q_dmg += (q_dmg * 0.75) * (shurikens - 1);
    }
    dmg += q_dmg;
    energy += 70;

    if (Ex->IsReady(E_SLOT, 0)) {
        dmg += EDmg(unit, energy);
        energy += 50;
    }

    dmg += AADmg(unit, 0);

    if (Ex->IsReady(R_SLOT, 0)) {
        dmg += RDmg(unit, dmg);
    }

    if (IGNITE_SLOT != -1 && Ex->IsReady(IGNITE_SLOT, 0)) {
        dmg += sdk::damage->get_summoner_spell_damage(Player, unit, summoner_spell_type::ignite);
    }

    return dmg;
}

void VenZed::UseQEx(game_object* unit, bool jungle) {
    if (!unit || !unit->is_valid() || !Ex->IsReady(Q_SLOT, 0)) return;

    if (Ex->Dist2D(unit) <= Q_RANGE) {
        if (jungle && Menu->UseJungleQ->get_bool() && Player->get_mana() > Menu->MinimumClearEnergy->get_int()) {
            sdk::common->cast_spell(Q_SLOT, unit->get_server_position());
        }
        if (unit->is_minion() && Menu->LastHitQ->get_bool() && Player->get_mana() >= Menu->MinimumLastHitEnergy->get_int()) {
            sdk::common->cast_spell(Q_SLOT, unit->get_server_position());
        }
    }
}

void VenZed::UseWEx(game_object* unit, bool jungle) {
    if (!unit || !unit->is_valid() || !Ex->IsReady(W_SLOT, 0) || WShadowExists()) return;

    if (jungle && Player->get_mana() >= Menu->MinimumClearEnergy->get_int()) {
        if (Ex->Dist2D(unit) <= W_RANGE + E_RANGE) {
            sdk::common->cast_spell(W_SLOT, unit->get_server_position());
        }
    }
}

void VenZed::UseEEx(game_object* unit, bool jungle) {
    if (!unit || !unit->is_valid() || !Ex->IsReady(E_SLOT, 0)) return;

    if (Player->get_mana() >= 50) {
        if (Ex->Dist2D(unit) <= E_RANGE) {
            if (jungle && Menu->UseJungleE->get_bool() && Player->get_mana() >= Menu->MinimumClearEnergy->get_int()) {
                sdk::common->cast_spell(E_SLOT);
            }
            if (unit->is_minion() && Menu->LastHitE->get_bool() && Player->get_mana() >= Menu->MinimumLastHitEnergy->get_int()) {
                sdk::common->cast_spell(E_SLOT);
            }
        }
    }
}

bool VenZed::SoloQ(math::vector3 sourcepos, game_object* unit) {
    if (Menu->LowFPSMode->get_bool()) return true;

    pred_sdk::spell_data spell_data;
    spell_data.source_position = sourcepos;
    spell_data.range = Q_RANGE;
    spell_data.radius = 50.f;
    spell_data.delay = 0.25f;
    spell_data.projectile_speed = 1700.f;
    spell_data.forbidden_collisions = { pred_sdk::collision_type::unit, pred_sdk::collision_type::hero };

    auto pred = sdk::prediction->predict(unit, spell_data);
    return pred.is_valid && pred.hitchance > pred_sdk::hitchance::low;
}

void VenZed::GetMaxWPositions(game_object* unit, math::vector3& wpos) {
    auto possiblePositions = std::vector<math::vector3>();
    // This is a heavy logic, for now we simplify it to avoid performance issues.
    // A full port would require careful optimization.
    for (int i = 0; i < 360; i += 30) {
        float angle = i * M_PI / 180.f;
        for (float r = 200; r <= W_RANGE; r += 100) {
            math::vector3 cCircle(unit->get_server_position().x + r * cos(angle), 0, unit->get_server_position().z + r * sin(angle));
            cCircle.y = g_sdk->nav_mesh->get_height_for_position(cCircle.x, cCircle.z);

            if (Ex->Dist2D(Player->get_server_position(), cCircle) > W_RANGE) continue;
            if (g_sdk->nav_mesh->is_in_wall(cCircle)) continue;

            if (SoloQ(cCircle, unit)) {
                possiblePositions.push_back(cCircle);
            }
        }
    }

    if (!possiblePositions.empty()) {
        std::sort(possiblePositions.begin(), possiblePositions.end(), [&](math::vector3 v1, math::vector3 v2) {
            return Ex->Dist2D(unit->get_server_position(), v1) < Ex->Dist2D(unit->get_server_position(), v2);
        });
        wpos = possiblePositions.front();
    } else {
        wpos = unit->get_server_position();
    }
}

bool VenZed::CanSwap(int spell_slot) {
    auto spell = Player->get_spell(spell_slot);
    if (!spell) return false;
    // Check for spell name, e.g., "ZedW2" or "ZedR2"
    return spell->get_name().find("2") != std::string::npos;
}

bool VenZed::Recent(const std::string& name, float time = 0.f) {
    auto current_time = g_sdk->clock_facade->get_game_time() * 1000;
    if (Ticks.find(name) == Ticks.end()) return false;
    return current_time - Ticks[name] < 500 + time - g_sdk->net_client->get_ping();
}
    if (spell_slot == W_SLOT) {
        return WShadowExists();
    } else if (spell_slot == R_SLOT) {
        return RShadowExists();
    }
    return false;
}

bool VenZed::Recent(const std::string& name, float time) {
    auto current_time = g_sdk->get_clock()->get_game_time() * 1000;
    return current_time - Ticks[name] < 500 + time - g_sdk->get_net_client()->get_ping();
}