#include "ven_zed_extensions.h"

#include <algorithm>
#include <cmath>

#include "ven_zed.h"

// Static member definition
ActionManager VenZedExtensions::Actions;

void VenZedExtensions::DrawDamageOnChampionHPBar(game_object* unit, double damage, const char* text, const math::vector4& color) {
    if (!unit || unit->is_dead())
        return;

    auto renderer   = g_sdk->get_renderer();
    auto hp_bar_pos = unit->get_hp_bar_position();

    if (hp_bar_pos.x > 0 && hp_bar_pos.y > 0) {
        float         damage_ratio = std::min(damage, static_cast<double>(unit->get_hp())) / unit->get_max_hp();
        math::vector2 bar_size(103 * damage_ratio, 8);
        math::vector2 bar_pos(hp_bar_pos.x + 10, hp_bar_pos.y + 20);

        math::vector2 end_pos(bar_pos.x + bar_size.x, bar_pos.y);
        math::vector2 text_pos(bar_pos.x + bar_size.x - 5, bar_pos.y - 7);

        // Draw damage bar
        renderer->add_filled_rect_2d(bar_pos, bar_size, color);

        // Draw damage indicator lines
        renderer->add_line_2d(end_pos, text_pos, {255, 255, 255, 255});
        renderer->add_line_2d(end_pos, {end_pos.x, end_pos.y + 8}, {255, 255, 255, 255});

        // Draw damage text
        if (text && strlen(text) > 0) {
            renderer->add_text_2d({text_pos.x - 13, text_pos.y - 13}, text, {255, 255, 255, 255}, 12);
        }
    }
}

double VenZedExtensions::AmplifyDamage(game_object* source, game_object* target) {
    double modifier = 1.0;

    // VEN doesn't have mastery system like old League, so we'll use a simplified damage calculation
    // This would need to be updated based on current rune system and VEN's API capabilities

    // Basic damage amplification based on target's health percentage
    if (target->get_hp_percent() < 40) {
        modifier += 0.06;  // Coup de Grace equivalent
    }

    // Check for isolation (no allies nearby)
    auto allies   = g_sdk->get_object_manager()->get_heroes();
    bool isolated = true;
    for (auto ally : allies) {
        if (ally && ally != source && ally->get_team_id() == source->get_team_id() && !ally->is_dead() &&
            ally->get_position().distance(source->get_position()) <= 800) {
            isolated = false;
            break;
        }
    }

    if (isolated) {
        modifier += 0.02;  // Isolation bonus
    }

    return modifier;
}

std::vector<game_object*> VenZedExtensions::GetInRange(const math::vector2& pos, float range, std::vector<game_object*> group) {
    std::vector<game_object*> result;

    for (auto obj : group) {
        if (obj && obj->is_valid() && !obj->is_dead()) {
            auto obj_pos = obj->get_server_position();
            if (Dist2D(pos, obj_pos) <= range) {
                result.push_back(obj);
            }
        }
    }

    return result;
}

ProjectionInfo* VenZedExtensions::ProjectOn(const math::vector2& point, const math::vector2& start, const math::vector2& end) {
    auto cx = point.x;
    auto cy = point.y;
    auto ax = start.x;
    auto ay = start.y;
    auto bx = end.x;
    auto by = end.y;

    auto rL = ((cx - ax) * (bx - ax) + (cy - ay) * (by - ay)) / (std::pow(bx - ax, 2) + std::pow(by - ay, 2));

    auto  pointLine = math::vector2(ax + rL * (bx - ax), ay + rL * (by - ay));
    float rS;

    if (rL < 0) {
        rS = 0;
    } else if (rL > 1) {
        rS = 1;
    } else {
        rS = rL;
    }

    auto isOnSegment  = (rS == rL) || std::isnan(rL);
    auto pointSegment = isOnSegment ? pointLine : math::vector2(ax + rS * (bx - ax), ay + rS * (by - ay));

    return new ProjectionInfo(pointSegment, isOnSegment);
}