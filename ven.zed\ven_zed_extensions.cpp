#include "ven_zed_extensions.h"
#include "ven_zed.h"

bool VenZedExtensions::IsReady(int slot, float time) {
    auto spell = VenZed::Player->get_spell(slot);
    if (!spell) return false;
    return spell->get_cooldown_expire() - g_sdk->clock_facade->get_game_time() <= time;
}

bool VenZedExtensions::IsValid(math::vector3 p) {
    return p.x != 0 && p.z != 0;
}

bool VenZedExtensions::IsValid(math::vector2 p) {
    return p.x != 0 && p.y != 0;
}

math::vector2 VenZedExtensions::To2D(math::vector3 p) {
    return math::vector2(p.x, p.z);
}

math::vector3 VenZedExtensions::To3D(math::vector2 p) {
    return math::vector3(p.x, g_sdk->nav_mesh->get_height_for_position(p.x, p.y), p.y);
}

math::vector2 VenZedExtensions::Perp(math::vector2 v) {
    return math::vector2(-v.y, v.x);
}

float VenZedExtensions::Dist2D(game_object* to) {
    return (VenZed::Player->get_server_position() - to->get_server_position()).length_2d();
}

float VenZedExtensions::Dist2D(game_object* from, game_object* to) {
    return (from->get_server_position() - to->get_server_position()).length_2d();
}

float VenZedExtensions::Dist2D(math::vector3 from, math::vector3 to) {
    return (from - to).length_2d();
}

float VenZedExtensions::Dist2D(math::vector2 from, math::vector2 to) {
    return (from - to).length();
}

float VenZedExtensions::Dist2D(math::vector2 from, math::vector3 to) {
    return (from - To2D(to)).length();
}

bool VenZedExtensions::UnderEnemyTurret(math::vector3 pos) {
    return g_sdk->turret_manager->is_position_under_turret(pos, turret_manager::team::enemy);
}

bool VenZedExtensions::UnderAllyTurret(math::vector3 pos) {
    return g_sdk->turret_manager->is_position_under_turret(pos, turret_manager::team::ally);
}

bool VenZedExtensions::IsKeyDown(menu_item* menuOption) {
    return g_sdk->hud_manager->is_key_down(menuOption->get_key());
}

std::vector<math::vector2> VenZedExtensions::CircleCircleIntersection(math::vector2 center1, math::vector2 center2, float radius1, float radius2) {
    auto D = Dist2D(center1, center2);

    if (D > radius1 + radius2 || (D <= std::abs(radius1 - radius2))) {
        return std::vector<math::vector2>();
    }

    auto A = (radius1 * radius1 - radius2 * radius2 + D * D) / (2 * D);
    auto H = static_cast<float>(std::sqrt(radius1 * radius1 - A * A));
    auto Direction = (center2 - center1).normalized();
    auto PA = center1 + A * Direction;
    auto S1 = PA + H * Perp(Direction);
    auto S2 = PA - H * Perp(Direction);
    return std::vector<math::vector2>({ S1, S2 });
}

void VenZedExtensions::DrawDamageOnChampionHPBar(game_object* unit, double damage, const char* text, const ::color& color) {
    if (!unit->is_on_screen()) return;

    auto bar_pos = unit->get_hp_bar_position();
    if (IsValid(bar_pos)) {
        float percent_health = (float)std::min(damage, unit->get_hp()) / unit->get_max_hp();
        float bar_width = 103 * percent_health;
        bar_pos = math::vector2(bar_pos.x + 10, bar_pos.y += 20);

        g_sdk->renderer->filled_rectangle(bar_pos, { bar_width, 8 }, color);
        if (text) {
            g_sdk->renderer->text(bar_pos + math::vector2(bar_width, -13), text, {255, 255, 255, 255}, 14);
        }
    }
}

std::vector<game_object*> VenZedExtensions::GetInRange(math::vector3 pos, float range, std::vector<game_object*> group) {
    std::vector<game_object*> list;
    for (auto o : group) {
        if (o != nullptr && o->is_valid() && !o->is_dead() && Dist2D(pos, o->get_server_position()) <= range) {
            list.push_back(o);
        }
    }
    return list;
}

int VenZedExtensions::CountInRange(game_object* unit, float range, std::vector<game_object*> units) {
    return GetInRange(unit->get_server_position(), range, units).size();
}

int VenZedExtensions::CountInRange(math::vector3 pos, float range, std::vector<game_object*> units) {
    return GetInRange(pos, range, units).size();
}

void DelayAction::OnGameUpdate() {
    auto current_time = (int)(g_sdk->clock_facade->get_game_time() * 1000);

    Actions.erase(std::remove_if(Actions.begin(), Actions.end(), [&](Action& args) {
        if (current_time >= args.Time) {
            args.Call();
            return true;
        }
        return false;
    }), Actions.end());

    ActionsGameObject.erase(std::remove_if(ActionsGameObject.begin(), ActionsGameObject.end(), [&](ActionGameObject& args) {
        if (current_time >= args.Time) {
            args.Call();
            return true;
        }
        return false;
    }), ActionsGameObject.end());

    ActionsPosition.erase(std::remove_if(ActionsPosition.begin(), ActionsPosition.end(), [&](ActionPosition& args) {
        if (current_time >= args.Time) {
            args.Call();
            return true;
        }
        return false;
    }), ActionsPosition.end());
}

void DelayAction::Add(int Time, std::function<void()> Callback) {
    Actions.emplace_back(Time, Callback);
}

void DelayAction::AddGameObject(int Time, game_object* Unit, std::function<void(game_object*)> Callback) {
    ActionsGameObject.emplace_back(Time, Unit, Callback);
}

void DelayAction::AddPosition(int Time, math::vector3 Position, std::function<void(math::vector3)> Callback) {
    ActionsPosition.emplace_back(Time, Position, Callback);
}