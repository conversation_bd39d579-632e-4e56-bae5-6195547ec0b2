#pragma once
#include <string>

#include "../sdk/sdk.hpp"

enum EvadeType { Targeted, SkillshotLine, SkillshotCircle, SelfCast };

class VenZedAvoider {
   public:
    float Radius;
    EvadeType eType;
    game_object* Emitter;
    std::string SpellName;
    std::string ChampName;
    
    VenZedAvoider(std::string name, EvadeType type, std::string champname)
        : Radius(0), Emitter(nullptr) {
        this->SpellName = name;
        this->eType = type;
        this->ChampName = champname;
    }
    static void GenerateAvoidList();
};

// Inline implementation
inline void VenZedAvoider::GenerateAvoidList() {
    // Initialize avoidance list for common threats
    // This would be populated with spell data that <PERSON><PERSON> should avoid
    // For now, we'll leave this as a placeholder
    // The original implementation would populate VenZed::AvoidList
}