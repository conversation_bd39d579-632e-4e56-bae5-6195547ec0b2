#include "ven_zed_module.h"
#include "ven_zed.h"

bool VenZedModule::Initialize() {
    VenZed::Player = g_sdk->object_manager->get_local_player();
    if (!VenZed::Player || VenZed::Player->get_char_name() != "Zed") {
        return false;
    }

    VenZed::Ex = new VenZedExtensions();
    VenZed::Modes = new VenZedModes();
    VenZedAvoider::GenerateAvoidList();
    
    VenZed::OnBoot();
    VenZed::Menu = new VenZedMenu(g_sdk->menu->get_main_category()->add_category("ven.zed", "VEN Zed"));

    sdk::event_manager->register_callback(sdk::event_type::on_update, OnUpdate);
    sdk::event_manager->register_callback(sdk::event_type::on_render, OnRender);
    sdk::event_manager->register_callback(sdk::event_type::on_process_spell_cast, OnProcessSpellCast);
    sdk::event_manager->register_callback(sdk::event_type::on_create_object, OnCreateObject);
    sdk::event_manager->register_callback(sdk::event_type::on_buff_gain, OnBuffGain);
    sdk::event_manager->register_callback(sdk::event_type::on_do_cast, OnDoCast);
    sdk::event_manager->register_callback(sdk::event_type::on_orbwalker_non_killable_minion, OnNonKillableMinion);

    return true;
}

void VenZedModule::Shutdown() {
    sdk::event_manager->unregister_callback(sdk::event_type::on_update, OnUpdate);
    sdk::event_manager->unregister_callback(sdk::event_type::on_render, OnRender);
    sdk::event_manager->unregister_callback(sdk::event_type::on_process_spell_cast, OnProcessSpellCast);
    sdk::event_manager->unregister_callback(sdk::event_type::on_create_object, OnCreateObject);
    sdk::event_manager->unregister_callback(sdk::event_type::on_buff_gain, OnBuffGain);
    sdk::event_manager->unregister_callback(sdk::event_type::on_do_cast, OnDoCast);
    sdk::event_manager->unregister_callback(sdk::event_type::on_orbwalker_non_killable_minion, OnNonKillableMinion);

    VenZed::OnShutdown();

    delete VenZed::Menu;
    delete VenZed::Modes;
    delete VenZed::Ex;
}

void VenZedModule::OnUpdate() {
    if (g_sdk->hud_manager->is_chat_open() || !g_sdk->is_window_focused()) {
        return;
    }
    VenZed::Modes->OnUpdate();
}

void VenZedModule::OnRender() {
    VenZed::Modes->OnRender();
}

void VenZedModule::OnProcessSpellCast(const spell_cast& args) {
    VenZed::Modes->OnSpellCast(args);
}

void VenZedModule::OnCreateObject(game_object* obj) {
    VenZed::Modes->OnCreateObj(obj);
}

void VenZedModule::OnBuffGain(game_object* unit, buff* buff_data) {
    VenZed::Modes->OnBuffAdd(unit, buff_data);
}

void VenZedModule::OnDoCast(const spell_cast& args) {
    VenZed::Modes->OnDoCast(args);
}

void VenZedModule::OnNonKillableMinion(game_object* minion) {
    VenZed::Modes->OnNonKillableMinion(minion);
}