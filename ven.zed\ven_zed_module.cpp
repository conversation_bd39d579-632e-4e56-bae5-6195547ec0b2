#include "ven_zed_module.h"
#include "ven_zed.h"

bool VenZedModule::Initialize() {
    VenZed::Player = g_sdk->object_manager->get_local_player();
    if (!VenZed::Player || VenZed::Player->get_char_name() != "Zed") {
        return false;
    }

    VenZed::Ex = new VenZedExtensions();
    VenZed::Modes = new VenZedModes();
    VenZedAvoider::GenerateAvoidList();

    VenZed::OnBoot();

    // Initialize menu - need to check the correct menu manager interface
    auto main_menu = g_sdk->menu_manager->add_category("ven.zed", "VEN Zed");
    VenZed::Menu = new VenZedMenu(main_menu);

    // Register event handlers using the correct VEN event system
    g_sdk->event_manager->add_on_update(OnUpdate);
    g_sdk->event_manager->add_on_render(OnRender);
    g_sdk->event_manager->add_on_process_spell_cast(OnProcessSpellCast);
    g_sdk->event_manager->add_on_create_object(OnCreateObject);
    g_sdk->event_manager->add_on_buff_gain(OnBuffGain);
    g_sdk->event_manager->add_on_do_cast(OnDoCast);
    g_sdk->event_manager->add_on_non_killable_minion(OnNonKillableMinion);

    g_sdk->log_console("[VEN Zed] Module initialized successfully");
    return true;
}

void VenZedModule::Shutdown() {
    VenZed::OnShutdown();

    if (VenZed::Menu) {
        delete VenZed::Menu;
        VenZed::Menu = nullptr;
    }

    if (VenZed::Modes) {
        delete VenZed::Modes;
        VenZed::Modes = nullptr;
    }

    if (VenZed::Ex) {
        delete VenZed::Ex;
        VenZed::Ex = nullptr;
    }

    g_sdk->log_console("[VEN Zed] Module shutdown complete");
}

void VenZedModule::OnUpdate() {
    if (g_sdk->hud_manager->is_chat_open() || !g_sdk->is_window_focused()) {
        return;
    }
    VenZed::Modes->OnUpdate();
}

void VenZedModule::OnRender() {
    VenZed::Modes->OnRender();
}

void VenZedModule::OnProcessSpellCast(const spell_cast& args) {
    VenZed::Modes->OnSpellCast(args);
}

void VenZedModule::OnCreateObject(game_object* obj) {
    VenZed::Modes->OnCreateObj(obj);
}

void VenZedModule::OnBuffGain(game_object* unit, buff* buff_data) {
    VenZed::Modes->OnBuffAdd(unit, buff_data);
}

void VenZedModule::OnDoCast(const spell_cast& args) {
    VenZed::Modes->OnDoCast(args);
}

void VenZedModule::OnNonKillableMinion(game_object* minion) {
    VenZed::Modes->OnNonKillableMinion(minion);
}