#include "ven_zed_modes.h"
#include "ven_zed.h"

void VenZedModes::Combo() {
    auto ultunit = sdk::target_selector->get_hero_target(VenZed::Menu->AssassinRange->get_int(), damage_type::physical);
    auto mainunit = sdk::target_selector->get_hero_target(VenZed::W_RANGE * 2, damage_type::physical);
    auto canr = VenZed::Menu->UseComboR->get_bool() && VenZed::Ex->IsReady(VenZed::R_SLOT, 0);
    auto engage = false;

    VenZed::CanUlt(ultunit, engage);
    VenZed::UseR(ultunit, engage);

    if (!engage || !canr || !VenZed::Marked.empty()) {
        VenZed::UseW(mainunit, false);
        VenZed::UseE(mainunit, false);
        VenZed::UseQ(mainunit, false);
    }
}

void VenZedModes::Harass() {
    if (VenZed::Player->get_mana_percent() < VenZed::Menu->MinimumHarassEnergy->get_int()) return;
    auto unit = sdk::target_selector->get_hero_target(VenZed::W_RANGE * 2, damage_type::physical);

    VenZed::UseW(unit, true);
    VenZed::UseE(unit, true);
    VenZed::UseQ(unit, true);
}

void VenZedModes::Flee() {
    sdk::orbwalker->move_to(g_sdk->hud_manager->get_cursor_position());
    if (VenZed::Ex->IsReady(VenZed::W_SLOT, 0) && VenZed::Menu->UseFleeW->get_bool()) {
        sdk::common->cast_spell(VenZed::W_SLOT, g_sdk->hud_manager->get_cursor_position());
    }
}

void VenZedModes::WaveClear() {
    if (VenZed::Player->get_mana() < VenZed::Menu->MinimumClearEnergy->get_int()) return;

    if (VenZed::Menu->LastHitE->get_bool() && VenZed::Ex->IsReady(VenZed::E_SLOT, 0)) {
        auto minions = g_sdk->object_manager->get_minions();
        int e_minions_in_range = 0;
        for (auto& minion : minions) {
            if (minion && minion->is_enemy() && minion->is_valid() && !minion->is_dead() && VenZed::Ex->Dist2D(minion) <= VenZed::E_RANGE) {
                e_minions_in_range++;
            }
        }
        if (e_minions_in_range >= VenZed::Menu->LastHitECount->get_int()) {
            sdk::common->cast_spell(VenZed::E_SLOT);
        }
    }

    if (VenZed::Menu->LastHitQ->get_bool() && VenZed::Ex->IsReady(VenZed::Q_SLOT, 0)) {
        // Simplified Q logic for waveclear, as multi-target prediction is complex.
        // OnNonKillableMinion handles precise last hitting.
    }
}

void VenZedModes::Jungling() {
    if (VenZed::Player->get_mana_percent() < VenZed::Menu->MinimumClearEnergy->get_int()) return;

    auto mobs = g_sdk->object_manager->get_monsters();
    std::sort(mobs.begin(), mobs.end(), VenZed::GetPriorityJungleTarget);

    if (!mobs.empty()) {
        auto mob = mobs.front();
        if (mob && mob->is_valid() && !mob->is_dead()) {
            bool enemies_nearby = VenZed::Ex->CountInRange(VenZed::Player, 1000, g_sdk->object_manager->get_heroes()) > 0;
            if (!VenZed::Menu->DontWJungleNearEnemy->get_bool() || !enemies_nearby) {
                if (VenZed::Menu->UseJungleW->get_bool()) VenZed::UseWEx(mob, true);
            }
            if (VenZed::Menu->UseJungleE->get_bool()) VenZed::UseEEx(mob, true);
            if (VenZed::Menu->UseJungleQ->get_bool()) VenZed::UseQEx(mob, true);
        }
    }
}

void VenZedModes::OnUpdate() {
    VenZed::Ex->Action->OnGameUpdate();

    for (auto it = VenZed::Marked.begin(); it != VenZed::Marked.end();) {
        if (it->second->is_dead() || !it->second->is_valid() || g_sdk->clock_facade->get_game_time() - it->first > 6) {
            it = VenZed::Marked.erase(it);
        } else {
            ++it;
        }
    }

    for (auto it = VenZed::WPositions.begin(); it != VenZed::WPositions.end();) {
        if (g_sdk->clock_facade->get_game_time() - it->first - 0.25f > 1 + g_sdk->net_client->get_ping() / 1000.0f) {
            it = VenZed::WPositions.erase(it);
        } else {
            ++it;
        }
    }

    for (auto it = VenZed::Shadows.begin(); it != VenZed::Shadows.end();) {
        if (!it->second || !it->second->is_valid() || g_sdk->clock_facade->get_game_time() - it->first > 10) {
            it = VenZed::Shadows.erase(it);
        } else {
            ++it;
        }
    }

    if (VenZed::Ex->IsKeyDown(VenZed::Menu->ComboKey)) Combo();
    if (VenZed::Ex->IsKeyDown(VenZed::Menu->HarassKey)) Harass();
    if (VenZed::Ex->IsKeyDown(VenZed::Menu->ClearKey)) { WaveClear(); Jungling(); }
    if (VenZed::Ex->IsKeyDown(VenZed::Menu->FleeKey)) Flee();
    Auto();
}

void VenZedModes::OnSpellCast(const spell_cast& args) {
    if (args.source && args.source->get_network_id() == VenZed::Player->get_network_id()) {
        VenZed::Ticks[args.spell_name] = g_sdk->clock_facade->get_game_time() * 1000;
        if (args.spell_name == "ZedW") {
            VenZed::WPositions[g_sdk->clock_facade->get_game_time()] = args.end_position;
        }
    }

    // R Avoider logic
    if (args.source && args.source->is_hero() && args.source->is_enemy()) {
        if (VenZed::Ex->IsReady(VenZed::R_SLOT, 0) && VenZed::Menu->UseRAvoider->get_bool()) {
            auto best_target = sdk::target_selector->get_hero_target(VenZed::R_RANGE, damage_type::physical);
            if (!best_target) return;

            std::string spell_name = args.spell_name;
            std::transform(spell_name.begin(), spell_name.end(), spell_name.begin(),
                           [](unsigned char c){ return std::tolower(c); });

            for (auto& entry : VenZed::AvoidList) {
                if (spell_name.find(entry.first) != std::string::npos) {
                    if (VenZed::Menu->SpellsToAvoid.count(entry.first) && VenZed::Menu->SpellsToAvoid[entry.first]->get_bool()) {
                        if (entry.second->eType == Targeted) {
                            if (args.target && args.target->get_network_id() == VenZed::Player->get_network_id()) {
                                if (!VenZed::RShadowExists()) {
                                    sdk::common->cast_spell(VenZed::R_SLOT, best_target);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

void VenZedModes::OnRender() {
    if (VenZed::Player->is_dead()) return;

    if (VenZed::Menu->DrawQ->get_bool()) g_sdk->renderer->add_circle_3d(VenZed::Player->get_position(), VenZed::Q_RANGE, { 102, 204, 204, 200 });
    if (VenZed::Menu->DrawW->get_bool()) g_sdk->renderer->add_circle_3d(VenZed::Player->get_position(), VenZed::W_RANGE, { 102, 204, 204, 200 });
    if (VenZed::Menu->DrawE->get_bool()) g_sdk->renderer->add_circle_3d(VenZed::Player->get_position(), VenZed::E_RANGE, { 102, 204, 204, 200 });
    if (VenZed::Menu->DrawR->get_bool()) g_sdk->renderer->add_circle_3d(VenZed::Player->get_position(), VenZed::R_RANGE, { 102, 204, 204, 200 });

    if (VenZed::Menu->DrawAssassinRange->get_bool()) {
        bool engage;
        auto xunit = sdk::target_selector->get_hero_target(VenZed::Menu->AssassinRange->get_int(), damage_type::physical);
        VenZed::CanUlt(xunit, engage);
        if (VenZed::Menu->UseComboR->get_bool() && VenZed::Ex->IsReady(VenZed::R_SLOT, 0) && engage) {
            g_sdk->renderer->add_circle_3d(VenZed::Player->get_position(), VenZed::Menu->AssassinRange->get_int(), { 255, 102, 0, 200 });
        }
    }

    if (VenZed::Menu->DrawComboDamage->get_bool()) {
        double energy;
        for (auto&& i : g_sdk->object_manager->get_heroes()) {
            if (i && i->is_enemy() && i->is_on_screen() && i->is_visible() && i->is_valid()) {
                VenZed::Ex->DrawDamageOnChampionHPBar(i, VenZed::CDmg(i, energy), VenZed::Menu->DebugDamage->get_bool() ? std::to_string(VenZed::CDmg(i, energy)).c_str() : nullptr, { 102, 204, 204, 200 });
            }
        }
    }
}

void VenZedModes::OnCreateObj(game_object* obj) {
    if (obj && obj->get_team_id() == VenZed::Player->get_team_id() && obj->get_char_name() == "Shadow") {
        VenZed::Shadows[g_sdk->clock_facade->get_game_time()] = obj;
    }
    if (obj && obj->get_object_name() == "Zed_Base_R_buf_tell.troy") {
        VenZed::Ticks["Lethal"] = g_sdk->clock_facade->get_game_time() * 1000;

        if (VenZed::CanSwap(VenZed::R_SLOT) && VenZed::Menu->SwapRIfDead->get_bool()) {
            auto r_shadow = VenZed::RShadow();
            if (r_shadow) {
                if (!VenZed::Ex->UnderEnemyTurret(r_shadow->get_server_position()) && VenZed::Ex->UnderEnemyTurret(VenZed::Player->get_server_position())) {
                    sdk::common->cast_spell(VenZed::R_SLOT);
                    return;
                }
                if (VenZed::Ex->Dist2D(r_shadow) > VenZed::W_RANGE) {
                     sdk::common->cast_spell(VenZed::R_SLOT);
                     return;
                }
            }
        }

        if (VenZed::Menu->LaughIfDead->get_bool()) {
            g_sdk->get_chat()->send_message("/l");
        }
    }
}

void VenZedModes::OnBuffAdd(game_object* unit, buff* buff_data) {
    if (unit->is_enemy() && buff_data->get_name() == "zedrdeathmark") {
        VenZed::Marked[g_sdk->clock_facade->get_game_time()] = unit;
    }
}

void VenZedModes::OnDoCast(const spell_cast& args) {
    if (args.source && args.source->get_network_id() == VenZed::Player->get_network_id() && args.is_auto_attack && args.target && args.target->is_hero()) {
        if (sdk::orbwalker->get_mode() == orbwalker_mode::combo) {
            auto use_item = [&](int id) {
                if (sdk::common->item->has_item(id) && sdk::common->item->is_item_ready(id)) {
                    VenZed::Ex->Action->Add(100 + g_sdk->net_client->get_ping(), [id]() { sdk::common->item->use_item(id); });
                }
            };
            use_item(VenZed::TIAMAT_ID);
            use_item(VenZed::HYDRA_ID);
            use_item(VenZed::TITANIC_ID);
        }
    }
}

void VenZedModes::Auto() {
    auto engage = false;
    auto unit = sdk::target_selector->get_hero_target(VenZed::W_RANGE * 2, damage_type::physical);
    VenZed::CanUlt(unit, engage);

    if (VenZed::Menu->AutoR->get_bool()) {
        VenZed::UseR(unit, engage, true);
    }

    if (VenZed::Ex->IsReady(VenZed::E_SLOT, 0) && VenZed::Menu->AutoEUnitInRage->get_bool() && !engage &&
        VenZed::Player->get_mana() > VenZed::Menu->AutoEUnitInRagePct->get_int()) {
        if (VenZed::Ex->CountInRange(VenZed::Player, VenZed::E_RANGE, g_sdk->object_manager->get_heroes()) > 0) {
            sdk::common->cast_spell(VenZed::E_SLOT);
        }
    }
}

void VenZedModes::OnNonKillableMinion(game_object* minion) {
    if (!minion || !minion->is_valid()) return;
    double energy = 0;

    if (VenZed::Menu->LastHitEUnkillable->get_bool() && VenZed::Ex->IsReady(VenZed::E_SLOT, 0) && VenZed::EDmg(minion, energy) >= minion->get_hp()) {
        VenZed::UseEEx(minion, false);
    } else if (VenZed::Menu->LastHitQUnkillable->get_bool() && VenZed::Ex->IsReady(VenZed::Q_SLOT, 0) && VenZed::QDmg(VenZed::Player, minion, energy) >= minion->get_hp()) {
        VenZed::UseQEx(minion, false);
    }
}